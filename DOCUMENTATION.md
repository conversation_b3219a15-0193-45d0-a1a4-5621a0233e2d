# Diginest - Digital Agency Application Documentation

## Overview

Diginest is a comprehensive Flutter-based digital agency application that showcases services, manages client interactions, and handles quotations and payments. The application is designed to be responsive and works across web, mobile, and desktop platforms.

**Tagline:** "Building Your Digital Future, One App at a Time"

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Key Features](#key-features)
3. [Technology Stack](#technology-stack)
4. [Project Structure](#project-structure)
5. [Core Modules](#core-modules)
6. [Firebase Integration](#firebase-integration)
7. [Payment Integration](#payment-integration)
8. [Responsive Design](#responsive-design)
9. [State Management](#state-management)
10. [Setup and Installation](#setup-and-installation)
11. [Configuration](#configuration)
12. [API Documentation](#api-documentation)

## Architecture Overview

The application follows a modular architecture with clear separation of concerns:

- **Presentation Layer**: Flutter widgets and screens
- **Business Logic Layer**: Providers and services
- **Data Layer**: Models and data services
- **External Services**: Firebase, Razorpay, Analytics

### Design Patterns Used

- **Provider Pattern**: For state management
- **Repository Pattern**: For data access
- **Service Layer Pattern**: For business logic
- **Factory Pattern**: For model creation

## Key Features

### 1. **Multi-Page Website Structure**
- **Home Page**: Hero section, services overview, portfolio highlights, testimonials, pricing overview
- **About Page**: Company story, mission/vision, team section, process overview
- **Services Page**: Detailed service offerings with individual service detail pages
- **Portfolio Page**: Project showcase with filtering capabilities and case studies
- **Blog Page**: Content management with search and filtering
- **Contact Page**: Contact form, location map, FAQ section
- **Pricing Page**: Pricing plans, calculator, comparison tool, custom plan builder

### 2. **Quotation Management System**
- Generate PDF quotations for clients
- Store quotations in Firebase
- Email quotations to clients
- Quotation history and management
- Search and filter quotations

### 3. **Payment Integration**
- Razorpay payment gateway integration
- Multiple payment methods (cards, UPI, wallets)
- Receipt generation
- Subscription management
- Payment analytics

### 4. **Content Management**
- Dynamic service data
- Portfolio project management
- Blog post management
- Team member profiles

### 5. **Analytics and Tracking**
- User interaction tracking
- Payment analytics
- A/B testing capabilities
- Performance monitoring

## Technology Stack

### Frontend
- **Flutter**: ^3.6.0 (Cross-platform framework)
- **Dart**: Programming language
- **Go Router**: ^13.2.0 (Navigation)
- **Provider**: ^6.1.2 (State management)

### UI/UX
- **Google Fonts**: ^6.1.0 (Typography)
- **Flutter SVG**: ^2.0.10+1 (Vector graphics)
- **Lottie**: ^3.1.0 (Animations)
- **Flutter Animate**: ^4.5.0 (Animations)
- **Simple Animations**: ^5.0.2 (Animations)

### Backend Services
- **Firebase Core**: ^3.13.0
- **Firebase Auth**: ^5.5.3 (Authentication)
- **Cloud Firestore**: ^5.6.7 (Database)
- **Firebase Storage**: ^12.4.5 (File storage)
- **Firebase Analytics**: ^11.4.5 (Analytics)

### Payment
- **Razorpay Flutter**: ^1.3.5 (Payment gateway)

### Additional Features
- **Google Maps Flutter**: ^2.5.3 (Maps integration)
- **PDF**: ^3.10.7 (PDF generation)
- **Share Plus**: ^7.2.1 (Sharing functionality)
- **URL Launcher**: ^6.2.5 (External links)
- **Responsive Framework**: ^1.1.1 (Responsive design)

## Project Structure

```
lib/
├── config/                 # Configuration files
│   ├── app_consonant.dart   # App constants
│   ├── firebase_config.dart # Firebase configuration
│   ├── razorpay_config.dart # Payment configuration
│   ├── responsive.dart      # Responsive breakpoints
│   ├── routes.dart         # App routing
│   └── theme.dart          # App theme
├── constants/              # App constants
├── data/                   # Static data
│   ├── portfolio_data.dart # Portfolio projects
│   └── service_data.dart   # Service information
├── models/                 # Data models
│   ├── contact_message.dart
│   ├── quotation_model.dart
│   ├── receipt.dart
│   ├── subscription.dart
│   └── user_model.dart
├── providers/              # State management
│   ├── auth_provider.dart
│   ├── data_provider.dart
│   └── quotation_history_provider.dart
├── screens/                # UI screens
│   ├── about/
│   ├── auth/
│   ├── blog/
│   ├── contact/
│   ├── features/
│   ├── home/
│   ├── legal/
│   ├── portfolio/
│   ├── pricing/
│   ├── quotations/
│   └── services/
├── services/               # Business logic services
│   ├── firebase_service.dart
│   ├── quotation_service.dart
│   ├── payment_analytics_service.dart
│   └── receipt_service.dart
├── utils/                  # Utility functions
│   ├── analytics_service.dart
│   ├── payment_service.dart
│   └── razorpay_service.dart
├── widgets/                # Reusable UI components
│   ├── common/
│   ├── home/
│   ├── about/
│   ├── services/
│   ├── portfolio/
│   ├── blog/
│   ├── pricing/
│   └── quotations/
└── main.dart              # App entry point
```

## Core Modules

### 1. Authentication Module
- User registration and login
- Firebase Authentication integration
- Profile management
- Session management

### 2. Services Module
- Service catalog display
- Service detail pages
- Technology stack information
- Process timelines

### 3. Portfolio Module
- Project showcase
- Category filtering
- Case study details
- Skills display

### 4. Quotation Module
- PDF generation
- Email functionality
- History management
- Search and filtering

### 5. Payment Module
- Razorpay integration
- Multiple payment methods
- Receipt generation
- Subscription tracking

### 6. Content Module
- Blog management
- Dynamic content loading
- Search functionality
- Category filtering

## Firebase Integration

### Configuration
- Project ID: `diginest-cf340`
- Web App ID: `1:457393771740:web:a36abdc394bce5263a1019`
- Storage Bucket: `diginest-cf340.firebasestorage.app`

### Services Used
1. **Authentication**: User management and authentication
2. **Firestore**: Document database for quotations and user data
3. **Storage**: File storage for PDFs and images
4. **Analytics**: User behavior tracking
5. **Hosting**: Web application hosting

### Data Structure
```
/quotations/{quotationId}
├── id: string
├── client_name: string
├── client_email: string
├── plan_name: string
├── plan_price: number
├── features: array
├── created_at: timestamp
└── valid_until: timestamp
```

## Payment Integration

### Razorpay Configuration
- Test Mode: Enabled by default
- Supported Methods: Cards, UPI, Wallets (Paytm, GPay)
- Currency: INR
- Receipt Generation: Automated

### Payment Flow
1. User selects pricing plan
2. Payment form collection
3. Razorpay checkout initialization
4. Payment processing
5. Receipt generation
6. Subscription creation

### Security Features
- Server-side payment verification
- Secure API key management
- Payment analytics tracking
- Error handling and logging

## Responsive Design

### Breakpoints
- Mobile: < 768px
- Tablet: 768px - 1024px
- Desktop: 1024px - 1440px
- Large Desktop: > 1440px

### Responsive Components
- Adaptive layouts for all screen sizes
- Mobile-first design approach
- Touch-friendly interactions
- Optimized navigation for mobile

## State Management

### Provider Pattern Implementation
- **AuthProvider**: User authentication state
- **DataProvider**: Application data management
- **QuotationHistoryProvider**: Quotation management

### State Persistence
- SharedPreferences for user preferences
- Firebase for user data
- Local storage for temporary data

## Setup and Installation

### Prerequisites
- Flutter SDK ^3.6.0
- Dart SDK
- Firebase account
- Razorpay account (for payments)

### Installation Steps
1. Clone the repository
2. Run `flutter pub get`
3. Configure Firebase (see Configuration section)
4. Configure Razorpay (see RAZORPAY_SETUP.md)
5. Run `flutter run`

### Platform-Specific Setup
- **Web**: Firebase hosting configuration
- **Android**: Google Services configuration
- **iOS**: Firebase iOS configuration

## Configuration

### Firebase Setup
1. Create Firebase project
2. Enable Authentication, Firestore, Storage, Analytics
3. Download configuration files
4. Update `firebase_options.dart`

### Razorpay Setup
1. Create Razorpay account
2. Get API keys
3. Update `razorpay_config.dart`
4. Configure webhooks (optional)

### Environment Variables
- Firebase configuration
- Razorpay API keys
- Analytics tracking IDs
- External service URLs

## API Documentation

### Quotation Service API

#### Generate Quotation PDF
```dart
Future<Uint8List> generateQuotationPdf(QuotationModel quotation)
```
Generates a PDF document for the given quotation.

#### Save Quotation
```dart
Future<void> saveQuotation(QuotationModel quotation)
```
Saves quotation to Firebase Firestore.

#### Email Quotation
```dart
Future<void> emailQuotation(QuotationModel quotation, String recipientEmail)
```
Sends quotation PDF via email.

### Payment Service API

#### Process Payment
```dart
void processPayment({
  required String planName,
  required double amount,
  required bool isAnnual,
  required String customerName,
  required String customerEmail,
  String? customerPhone,
  String description = 'Diginest Subscription',
})
```
Initiates Razorpay payment process.

#### Verify Payment
```dart
Future<bool> verifyPayment(String paymentId, String orderId, String signature)
```
Verifies payment authenticity.

### Firebase Service API

#### Authentication
```dart
Future<UserCredential> signInWithEmailAndPassword(String email, String password)
Future<UserCredential> createUserWithEmailAndPassword(String email, String password)
Future<void> signOut()
```

#### Firestore Operations
```dart
Future<void> addDocument(String collection, Map<String, dynamic> data)
Future<DocumentSnapshot> getDocument(String collection, String documentId)
Future<QuerySnapshot> getCollection(String collection)
```

## Pricing Plans

### Starter Plan
- **Price**: ₹3,299/month (₹4,099 monthly)
- **Features**:
  - Up to 5 pages
  - Basic SEO optimization
  - Mobile responsive design
  - Contact form
  - Social media integration

### Professional Plan (Most Popular)
- **Price**: ₹6,599/month (₹8,299 monthly)
- **Features**:
  - Up to 10 pages
  - Advanced SEO optimization
  - Mobile responsive design
  - Contact form
  - Social media integration
  - E-commerce functionality
  - Custom animations
  - Basic analytics

### Enterprise Plan
- **Price**: ₹12,499/month (₹15,799 monthly)
- **Features**:
  - Unlimited pages
  - Advanced SEO optimization
  - Mobile responsive design
  - Contact form
  - Social media integration
  - E-commerce functionality
  - Custom animations
  - Advanced analytics
  - Priority support
  - Monthly maintenance

## Services Offered

### 1. Android App Development
- Native Android development with Kotlin/Java
- Material Design implementation
- Google Play Store optimization
- Performance optimization
- Testing and quality assurance

### 2. iOS App Development
- Native iOS development with Swift/Objective-C
- Human Interface Guidelines compliance
- App Store optimization
- Performance optimization
- Testing and quality assurance

### 3. Web Development
- Responsive web applications
- Progressive Web Apps (PWAs)
- Modern frameworks (React, Angular, Vue.js)
- SEO optimization
- Performance optimization

### 4. Cross-Platform Development
- Flutter applications
- React Native applications
- Single codebase solutions
- Platform-specific optimizations
- Consistent user experience

### 5. UI/UX Design
- User research and analysis
- Wireframing and prototyping
- Visual design
- Usability testing
- Design system creation

### 6. Cloud Services & DevOps
- Cloud infrastructure setup
- CI/CD pipeline implementation
- Monitoring and logging
- Security implementation
- Performance optimization

## User Roles and Permissions

### Guest Users
- View public content
- Browse services and portfolio
- Read blog posts
- Contact form submission

### Registered Users
- All guest user permissions
- Save quotations
- Access quotation history
- Profile management
- Subscription management

### Admin Users (Future Implementation)
- All user permissions
- Content management
- User management
- Analytics dashboard
- System configuration

## Security Features

### Data Protection
- HTTPS encryption for all communications
- Firebase security rules
- Input validation and sanitization
- XSS protection
- CSRF protection

### Payment Security
- PCI DSS compliant payment processing
- Secure API key management
- Payment verification
- Fraud detection
- Secure data transmission

### Authentication Security
- Email verification
- Password strength requirements
- Session management
- Account lockout protection
- Secure password reset

## Performance Optimization

### Frontend Optimization
- Lazy loading of images and components
- Code splitting and tree shaking
- Minification and compression
- Caching strategies
- Progressive loading

### Backend Optimization
- Database query optimization
- Caching mechanisms
- CDN integration
- Image optimization
- API response optimization

## Testing Strategy

### Unit Testing
- Model testing
- Service testing
- Utility function testing
- Provider testing

### Integration Testing
- API integration testing
- Firebase integration testing
- Payment integration testing
- Navigation testing

### Widget Testing
- UI component testing
- User interaction testing
- Responsive design testing
- Accessibility testing

### End-to-End Testing
- User journey testing
- Cross-platform testing
- Performance testing
- Security testing

## Deployment

### Web Deployment
- Firebase Hosting
- Custom domain configuration
- SSL certificate setup
- CDN configuration

### Mobile Deployment
- Google Play Store (Android)
- Apple App Store (iOS)
- App signing and security
- Store optimization

### CI/CD Pipeline
- Automated testing
- Build automation
- Deployment automation
- Version management

## Monitoring and Analytics

### Application Monitoring
- Firebase Analytics
- Performance monitoring
- Error tracking
- User behavior analysis

### Business Analytics
- Conversion tracking
- Revenue analytics
- User engagement metrics
- A/B testing results

## Maintenance and Support

### Regular Maintenance
- Security updates
- Performance optimization
- Bug fixes
- Feature updates

### Support Channels
- Email support
- Documentation
- FAQ section
- Community forums

## Future Enhancements

### Planned Features
- Admin dashboard
- Advanced analytics
- Multi-language support
- Advanced CMS
- API integrations

### Technology Upgrades
- Flutter version updates
- Firebase feature adoption
- Performance improvements
- Security enhancements

## Contributing

### Development Guidelines
- Code style standards
- Git workflow
- Testing requirements
- Documentation standards

### Code Review Process
- Pull request requirements
- Review criteria
- Approval process
- Merge guidelines

## License

This project is proprietary software owned by Diginest. All rights reserved.

## Contact Information

- **Company**: Diginest
- **Email**: <EMAIL>
- **Phone**: +91 **********
- **Website**: https://diginest.com
- **Address**: Lalgopalganj, Prayagraj, Uttar Pradesh, India

---

*This documentation is maintained by the Diginest development team and is updated regularly to reflect the current state of the application.*
