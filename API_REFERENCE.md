# Diginest API Reference

## Overview

This document provides comprehensive API reference for the Diginest Flutter application, including service methods, data models, and integration patterns.

## Table of Contents

1. [Authentication API](#authentication-api)
2. [Quotation Service API](#quotation-service-api)
3. [Payment Service API](#payment-service-api)
4. [Firebase Service API](#firebase-service-api)
5. [Analytics Service API](#analytics-service-api)
6. [Data Models](#data-models)
7. [<PERSON>rro<PERSON> Handling](#error-handling)
8. [Configuration](#configuration)

## Authentication API

### AuthProvider Class

The `AuthProvider` class manages user authentication state and operations.

#### Methods

##### `initialize()`
```dart
Future<void> initialize()
```
Initializes the authentication provider and checks for existing user sessions.

**Returns:** `Future<void>`

**Example:**
```dart
final authProvider = AuthProvider();
await authProvider.initialize();
```

##### `signIn()`
```dart
Future<bool> signIn({
  required String email,
  required String password,
})
```
Signs in a user with email and password.

**Parameters:**
- `email` (String): User's email address
- `password` (String): User's password

**Returns:** `Future<bool>` - `true` if successful, `false` otherwise

**Example:**
```dart
final success = await authProvider.signIn(
  email: '<EMAIL>',
  password: 'password123',
);
```

##### `createAccount()`
```dart
Future<bool> createAccount({
  required String email,
  required String password,
  required String name,
  String? phone,
  String? company,
})
```
Creates a new user account.

**Parameters:**
- `email` (String): User's email address
- `password` (String): User's password
- `name` (String): User's full name
- `phone` (String?): Optional phone number
- `company` (String?): Optional company name

**Returns:** `Future<bool>` - `true` if successful, `false` otherwise

##### `signOut()`
```dart
Future<void> signOut()
```
Signs out the current user.

**Returns:** `Future<void>`

#### Properties

##### `isAuthenticated`
```dart
bool get isAuthenticated
```
Returns whether a user is currently authenticated.

##### `currentUser`
```dart
UserModel? get currentUser
```
Returns the current authenticated user or `null` if not authenticated.

##### `isLoading`
```dart
bool get isLoading
```
Returns whether an authentication operation is in progress.

## Quotation Service API

### QuotationService Class

The `QuotationService` class handles quotation generation, storage, and management.

#### Methods

##### `generateQuotationPdf()`
```dart
Future<Uint8List> generateQuotationPdf(QuotationModel quotation)
```
Generates a PDF document for the given quotation.

**Parameters:**
- `quotation` (QuotationModel): The quotation data

**Returns:** `Future<Uint8List>` - PDF file as bytes

**Example:**
```dart
final quotationService = QuotationService();
final pdfBytes = await quotationService.generateQuotationPdf(quotation);
```

##### `saveQuotation()`
```dart
Future<void> saveQuotation(QuotationModel quotation)
```
Saves a quotation to Firebase Firestore.

**Parameters:**
- `quotation` (QuotationModel): The quotation to save

**Returns:** `Future<void>`

##### `getQuotations()`
```dart
Future<List<QuotationModel>> getQuotations()
```
Retrieves all quotations from Firebase Firestore.

**Returns:** `Future<List<QuotationModel>>` - List of quotations

##### `emailQuotation()`
```dart
Future<void> emailQuotation(
  QuotationModel quotation,
  String recipientEmail,
)
```
Sends a quotation PDF via email.

**Parameters:**
- `quotation` (QuotationModel): The quotation to email
- `recipientEmail` (String): Recipient's email address

**Returns:** `Future<void>`

##### `shareQuotation()`
```dart
Future<void> shareQuotation(QuotationModel quotation)
```
Shares a quotation PDF using the device's share functionality.

**Parameters:**
- `quotation` (QuotationModel): The quotation to share

**Returns:** `Future<void>`

##### `generateQuotationId()`
```dart
String generateQuotationId()
```
Generates a unique quotation ID.

**Returns:** `String` - Unique quotation ID

## Payment Service API

### RazorpayService Class

The `RazorpayService` class handles payment processing through Razorpay.

#### Methods

##### `initialize()`
```dart
void initialize({
  required Function(PaymentSuccessResponse) onPaymentSuccess,
  required Function(PaymentFailureResponse) onPaymentFailure,
  required Function(ExternalWalletResponse) onExternalWallet,
})
```
Initializes Razorpay with callback functions.

**Parameters:**
- `onPaymentSuccess` (Function): Success callback
- `onPaymentFailure` (Function): Failure callback
- `onExternalWallet` (Function): External wallet callback

##### `processPayment()`
```dart
void processPayment({
  required String planName,
  required double amount,
  required bool isAnnual,
  required String customerName,
  required String customerEmail,
  String? customerPhone,
  String description = 'Diginest Subscription',
})
```
Initiates a payment process.

**Parameters:**
- `planName` (String): Name of the selected plan
- `amount` (double): Payment amount in INR
- `isAnnual` (bool): Whether billing is annual
- `customerName` (String): Customer's name
- `customerEmail` (String): Customer's email
- `customerPhone` (String?): Optional phone number
- `description` (String): Payment description

##### `verifyPayment()`
```dart
Future<bool> verifyPayment(
  String paymentId,
  String orderId,
  String signature,
)
```
Verifies a payment's authenticity.

**Parameters:**
- `paymentId` (String): Razorpay payment ID
- `orderId` (String): Order ID
- `signature` (String): Payment signature

**Returns:** `Future<bool>` - `true` if payment is valid

##### `generateReceipt()`
```dart
Future<String> generateReceipt({
  required String paymentId,
  required String planName,
  required double amount,
  required bool isAnnual,
  required String customerName,
  required String customerEmail,
})
```
Generates a receipt for a successful payment.

**Parameters:**
- `paymentId` (String): Razorpay payment ID
- `planName` (String): Plan name
- `amount` (double): Payment amount
- `isAnnual` (bool): Billing type
- `customerName` (String): Customer name
- `customerEmail` (String): Customer email

**Returns:** `Future<String>` - Receipt ID

## Firebase Service API

### FirebaseService Class

The `FirebaseService` class provides Firebase integration methods.

#### Methods

##### `initialize()`
```dart
Future<void> initialize()
```
Initializes Firebase services.

**Returns:** `Future<void>`

##### `saveDocument()`
```dart
Future<void> saveDocument(
  String collection,
  String documentId,
  Map<String, dynamic> data,
)
```
Saves a document to Firestore.

**Parameters:**
- `collection` (String): Collection name
- `documentId` (String): Document ID
- `data` (Map<String, dynamic>): Document data

**Returns:** `Future<void>`

##### `getDocument()`
```dart
Future<DocumentSnapshot> getDocument(
  String collection,
  String documentId,
)
```
Retrieves a document from Firestore.

**Parameters:**
- `collection` (String): Collection name
- `documentId` (String): Document ID

**Returns:** `Future<DocumentSnapshot>` - Document snapshot

##### `getCollection()`
```dart
Future<QuerySnapshot> getCollection(String collection)
```
Retrieves all documents from a collection.

**Parameters:**
- `collection` (String): Collection name

**Returns:** `Future<QuerySnapshot>` - Query snapshot

##### `uploadFile()`
```dart
Future<String> uploadFile(
  String path,
  Uint8List data,
  String contentType,
)
```
Uploads a file to Firebase Storage.

**Parameters:**
- `path` (String): Storage path
- `data` (Uint8List): File data
- `contentType` (String): MIME type

**Returns:** `Future<String>` - Download URL

##### `uploadQuotationPdf()`
```dart
Future<String> uploadQuotationPdf(
  String quotationId,
  Uint8List pdfBytes,
)
```
Uploads a quotation PDF to Firebase Storage.

**Parameters:**
- `quotationId` (String): Quotation ID
- `pdfBytes` (Uint8List): PDF file bytes

**Returns:** `Future<String>` - Download URL

## Analytics Service API

### AnalyticsService Class

The `AnalyticsService` class handles analytics tracking.

#### Methods

##### `trackScreenView()`
```dart
void trackScreenView(String screenName)
```
Tracks a screen view event.

**Parameters:**
- `screenName` (String): Name of the screen

##### `trackEvent()`
```dart
void trackEvent(
  String eventName, {
  Map<String, dynamic>? properties,
})
```
Tracks a custom event.

**Parameters:**
- `eventName` (String): Event name
- `properties` (Map<String, dynamic>?): Optional event properties

##### `trackUserProperty()`
```dart
void trackUserProperty(String propertyName, String value)
```
Sets a user property for analytics.

**Parameters:**
- `propertyName` (String): Property name
- `value` (String): Property value

## Data Models

### UserModel

```dart
class UserModel {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String? profileImageId;
  final String? company;
  final String role;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.profileImageId,
    this.company,
    this.role = 'user',
    DateTime? createdAt,
    DateTime? updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json);
  Map<String, dynamic> toJson();
  UserModel copyWith({...});
}
```

### QuotationModel

```dart
class QuotationModel {
  final String id;
  final String clientName;
  final String clientEmail;
  final String? clientPhone;
  final String? clientCompany;
  final String planName;
  final double planPrice;
  final bool isAnnual;
  final List<String> features;
  final DateTime createdAt;
  final DateTime validUntil;
  final String? notes;

  QuotationModel({
    required this.id,
    required this.clientName,
    required this.clientEmail,
    this.clientPhone,
    this.clientCompany,
    required this.planName,
    required this.planPrice,
    required this.isAnnual,
    required this.features,
    DateTime? createdAt,
    DateTime? validUntil,
    this.notes,
  });

  factory QuotationModel.fromMap(Map<String, dynamic> map);
  Map<String, dynamic> toMap();
  String get formattedCreatedAt;
  String get formattedValidUntil;
}
```

### Receipt

```dart
class Receipt {
  final String id;
  final String paymentId;
  final String planName;
  final double amount;
  final String customerName;
  final String customerEmail;
  final DateTime createdAt;
  final bool isAnnual;

  Receipt({
    required this.id,
    required this.paymentId,
    required this.planName,
    required this.amount,
    required this.customerName,
    required this.customerEmail,
    required this.createdAt,
    required this.isAnnual,
  });

  factory Receipt.fromMap(Map<String, dynamic> map);
  Map<String, dynamic> toMap();
  String get formattedAmount;
  String get formattedDate;
}
```

### Subscription

```dart
class Subscription {
  final String id;
  final String customerId;
  final String planName;
  final String paymentId;
  final double amount;
  final bool isAnnual;
  final String status;
  final DateTime createdAt;
  final DateTime expiresAt;

  Subscription({
    required this.id,
    required this.customerId,
    required this.planName,
    required this.paymentId,
    required this.amount,
    required this.isAnnual,
    required this.status,
    required this.createdAt,
    required this.expiresAt,
  });

  factory Subscription.fromMap(Map<String, dynamic> map);
  Map<String, dynamic> toMap();
  bool get isActive;
  bool get isExpired;
  String get formattedExpiryDate;
}
```

## Error Handling

### Exception Classes

#### `AuthException`
```dart
class AuthException implements Exception {
  final String message;
  final String code;

  AuthException(this.message, this.code);

  @override
  String toString() => 'AuthException: $message (Code: $code)';
}
```

#### `QuotationException`
```dart
class QuotationException implements Exception {
  final String message;
  final String operation;

  QuotationException(this.message, this.operation);

  @override
  String toString() => 'QuotationException: $message (Operation: $operation)';
}
```

#### `PaymentException`
```dart
class PaymentException implements Exception {
  final String message;
  final String? paymentId;
  final String? errorCode;

  PaymentException(this.message, {this.paymentId, this.errorCode});

  @override
  String toString() => 'PaymentException: $message';
}
```

### Error Handling Patterns

#### Try-Catch with Specific Exceptions
```dart
try {
  await quotationService.saveQuotation(quotation);
} on QuotationException catch (e) {
  // Handle quotation-specific errors
  print('Quotation error: ${e.message}');
} on FirebaseException catch (e) {
  // Handle Firebase errors
  print('Firebase error: ${e.message}');
} catch (e) {
  // Handle general errors
  print('Unexpected error: $e');
}
```

#### Provider Error Handling
```dart
class AuthProvider extends ChangeNotifier {
  String? _errorMessage;

  String? get errorMessage => _errorMessage;

  Future<bool> signIn(String email, String password) async {
    try {
      _errorMessage = null;
      // Perform sign in
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }
}
```

## Configuration

### Firebase Configuration

#### `firebase_options.dart`
```dart
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) return web;
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      default:
        throw UnsupportedError('Platform not supported');
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'your-api-key',
    appId: 'your-app-id',
    messagingSenderId: 'your-sender-id',
    projectId: 'your-project-id',
    authDomain: 'your-auth-domain',
    storageBucket: 'your-storage-bucket',
  );
}
```

### Razorpay Configuration

#### `razorpay_config.dart`
```dart
class RazorpayConfig {
  static const String testKey = 'rzp_test_YOUR_TEST_KEY';
  static const String liveKey = 'rzp_live_YOUR_LIVE_KEY';
  static const bool isLiveMode = false;

  static String get apiKey => isLiveMode ? liveKey : testKey;
  static const String companyName = 'Diginest';
  static const String primaryColor = '#6200EA';
  static const String companyLogo = 'https://your-domain.com/logo.png';
}
```

### App Configuration

#### `app_consonant.dart`
```dart
class AppConsonant {
  // API Endpoints
  static const String baseUrl = 'https://api.diginest.com';
  static const String quotationEndpoint = '/quotations';
  static const String paymentEndpoint = '/payments';

  // Asset Paths
  static const String logoPath = 'assets/logos/diginestPng.png';
  static const String heroImage = 'assets/images/hero.png';

  // App Constants
  static const String appName = 'Diginest';
  static const String appVersion = '1.0.0';
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+91 **********';
}
```

## Usage Examples

### Complete Authentication Flow
```dart
class AuthExample {
  final AuthProvider _authProvider = AuthProvider();

  Future<void> performLogin(String email, String password) async {
    try {
      final success = await _authProvider.signIn(
        email: email,
        password: password,
      );

      if (success) {
        print('Login successful');
        // Navigate to home screen
      } else {
        print('Login failed: ${_authProvider.errorMessage}');
      }
    } catch (e) {
      print('Login error: $e');
    }
  }
}
```

### Complete Quotation Flow
```dart
class QuotationExample {
  final QuotationService _quotationService = QuotationService();

  Future<void> createAndEmailQuotation() async {
    try {
      // Create quotation
      final quotation = QuotationModel(
        id: _quotationService.generateQuotationId(),
        clientName: 'John Doe',
        clientEmail: '<EMAIL>',
        planName: 'Professional',
        planPrice: 6599.0,
        isAnnual: true,
        features: ['Feature 1', 'Feature 2'],
      );

      // Save quotation
      await _quotationService.saveQuotation(quotation);

      // Email quotation
      await _quotationService.emailQuotation(
        quotation,
        quotation.clientEmail,
      );

      print('Quotation created and emailed successfully');
    } catch (e) {
      print('Quotation error: $e');
    }
  }
}
```

### Complete Payment Flow
```dart
class PaymentExample {
  final RazorpayService _razorpayService = RazorpayService();

  void initializePayment() {
    _razorpayService.initialize(
      onPaymentSuccess: (response) {
        print('Payment successful: ${response.paymentId}');
        _handlePaymentSuccess(response);
      },
      onPaymentFailure: (response) {
        print('Payment failed: ${response.message}');
      },
      onExternalWallet: (response) {
        print('External wallet: ${response.walletName}');
      },
    );
  }

  void processPayment() {
    _razorpayService.processPayment(
      planName: 'Professional',
      amount: 6599.0,
      isAnnual: true,
      customerName: 'John Doe',
      customerEmail: '<EMAIL>',
    );
  }

  Future<void> _handlePaymentSuccess(PaymentSuccessResponse response) async {
    try {
      // Verify payment
      final isValid = await _razorpayService.verifyPayment(
        response.paymentId!,
        response.orderId!,
        response.signature!,
      );

      if (isValid) {
        // Generate receipt
        final receiptId = await _razorpayService.generateReceipt(
          paymentId: response.paymentId!,
          planName: 'Professional',
          amount: 6599.0,
          isAnnual: true,
          customerName: 'John Doe',
          customerEmail: '<EMAIL>',
        );

        print('Receipt generated: $receiptId');
      }
    } catch (e) {
      print('Payment verification error: $e');
    }
  }
}
```

## Best Practices

### 1. Error Handling
- Always wrap async operations in try-catch blocks
- Use specific exception types for better error handling
- Provide meaningful error messages to users
- Log errors for debugging purposes

### 2. State Management
- Use Provider pattern for consistent state management
- Notify listeners when state changes
- Handle loading states appropriately
- Implement proper error states

### 3. Security
- Never expose API keys in client code
- Validate all user inputs
- Use Firebase security rules
- Implement proper authentication checks

### 4. Performance
- Use lazy loading for large datasets
- Implement proper caching strategies
- Optimize database queries
- Use pagination for large lists

### 5. Testing
- Write unit tests for all service methods
- Mock external dependencies
- Test error scenarios
- Implement integration tests

---

This API reference provides comprehensive documentation for integrating with the Diginest application services. For additional support, contact the development <NAME_EMAIL>.
