# Diginest - Technical Architecture Documentation

## System Architecture Overview

The Diginest application follows a modern, scalable architecture designed for cross-platform deployment with robust backend services.

## Architecture Diagram

```mermaid
graph TB
    subgraph "Client Layer"
        A[Flutter Web App]
        B[Flutter Mobile App]
        C[Flutter Desktop App]
    end
    
    subgraph "Presentation Layer"
        D[Screens & Widgets]
        E[Responsive UI Components]
        F[Animation Controllers]
    end
    
    subgraph "State Management"
        G[Provider Pattern]
        H[AuthProvider]
        I[DataProvider]
        J[QuotationProvider]
    end
    
    subgraph "Business Logic Layer"
        K[Services]
        L[Firebase Service]
        M[Quotation Service]
        N[Payment Service]
        O[Analytics Service]
    end
    
    subgraph "Data Layer"
        P[Models]
        Q[User Model]
        R[Quotation Model]
        S[Receipt Model]
    end
    
    subgraph "External Services"
        T[Firebase]
        U[Razorpay]
        V[Google Maps]
        W[Email Service]
    end
    
    subgraph "Firebase Services"
        X[Authentication]
        Y[Firestore Database]
        Z[Storage]
        AA[Analytics]
        BB[Hosting]
    end
    
    A --> D
    B --> D
    C --> D
    D --> G
    E --> G
    F --> G
    G --> K
    H --> L
    I --> L
    J --> M
    K --> P
    L --> T
    M --> T
    N --> U
    O --> AA
    T --> X
    T --> Y
    T --> Z
    T --> AA
    T --> BB
```

## Component Architecture

### 1. Presentation Layer Components

#### Screens
- **HomeScreen**: Landing page with hero section and service overview
- **AboutScreen**: Company information and team details
- **ServicesScreen**: Service catalog with detailed descriptions
- **PortfolioScreen**: Project showcase with filtering capabilities
- **BlogScreen**: Content management and blog posts
- **ContactScreen**: Contact form and location information
- **PricingScreen**: Pricing plans and quotation generation
- **QuotationHistoryScreen**: Quotation management dashboard

#### Widgets
- **Common Widgets**: Reusable UI components
- **Responsive Components**: Adaptive layouts for different screen sizes
- **Animation Components**: Lottie and custom animations
- **Form Components**: Input validation and submission handling

### 2. State Management Architecture

#### Provider Pattern Implementation
```dart
MultiProvider(
  providers: [
    ChangeNotifierProvider<AuthProvider>(create: (_) => AuthProvider()),
    ChangeNotifierProvider<DataProvider>(create: (_) => DataProvider()),
    ChangeNotifierProvider<QuotationHistoryProvider>(
      create: (_) => QuotationHistoryProvider()
    ),
  ],
  child: MyApp(),
)
```

#### State Flow
1. **UI Events** → Trigger provider methods
2. **Provider Methods** → Call service layer
3. **Service Layer** → Interact with external APIs
4. **State Updates** → Notify UI components
5. **UI Rebuild** → Reflect new state

### 3. Service Layer Architecture

#### Firebase Service
```dart
class FirebaseService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  
  // Authentication methods
  Future<UserCredential> signIn(String email, String password);
  Future<UserCredential> createUser(String email, String password);
  
  // Firestore operations
  Future<void> saveDocument(String collection, Map<String, dynamic> data);
  Future<DocumentSnapshot> getDocument(String collection, String id);
  
  // Storage operations
  Future<String> uploadFile(String path, Uint8List data);
}
```

#### Quotation Service
```dart
class QuotationService {
  // PDF generation
  Future<Uint8List> generateQuotationPdf(QuotationModel quotation);
  
  // Storage operations
  Future<void> saveQuotation(QuotationModel quotation);
  Future<List<QuotationModel>> getQuotations();
  
  // Email functionality
  Future<void> emailQuotation(QuotationModel quotation, String email);
}
```

#### Payment Service (Razorpay)
```dart
class RazorpayService {
  void processPayment({
    required String planName,
    required double amount,
    required String customerEmail,
  });
  
  Future<bool> verifyPayment(String paymentId, String signature);
  Future<String> generateReceipt(String paymentId);
}
```

### 4. Data Layer Architecture

#### Model Classes
```dart
// User Model
class UserModel {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String? company;
  final String role;
  final DateTime createdAt;
}

// Quotation Model
class QuotationModel {
  final String id;
  final String clientName;
  final String clientEmail;
  final String planName;
  final double planPrice;
  final bool isAnnual;
  final List<String> features;
  final DateTime createdAt;
  final DateTime validUntil;
}

// Receipt Model
class Receipt {
  final String id;
  final String paymentId;
  final String planName;
  final double amount;
  final String customerEmail;
  final DateTime createdAt;
}
```

## Data Flow Architecture

### 1. User Authentication Flow
```
User Input → AuthProvider → FirebaseService → Firebase Auth → User State Update → UI Rebuild
```

### 2. Quotation Generation Flow
```
Pricing Selection → QuotationDialog → QuotationService → PDF Generation → Firebase Storage → Email Service
```

### 3. Payment Processing Flow
```
Payment Form → RazorpayService → Razorpay Gateway → Payment Verification → Receipt Generation → Subscription Creation
```

## Database Schema

### Firestore Collections

#### Users Collection
```json
{
  "users": {
    "userId": {
      "name": "string",
      "email": "string",
      "phone": "string",
      "company": "string",
      "role": "string",
      "createdAt": "timestamp",
      "updatedAt": "timestamp"
    }
  }
}
```

#### Quotations Collection
```json
{
  "quotations": {
    "quotationId": {
      "clientName": "string",
      "clientEmail": "string",
      "clientPhone": "string",
      "clientCompany": "string",
      "planName": "string",
      "planPrice": "number",
      "isAnnual": "boolean",
      "features": ["string"],
      "createdAt": "timestamp",
      "validUntil": "timestamp",
      "notes": "string",
      "pdfUrl": "string"
    }
  }
}
```

#### Subscriptions Collection
```json
{
  "subscriptions": {
    "subscriptionId": {
      "customerId": "string",
      "planName": "string",
      "paymentId": "string",
      "amount": "number",
      "isAnnual": "boolean",
      "status": "string",
      "createdAt": "timestamp",
      "expiresAt": "timestamp"
    }
  }
}
```

## Security Architecture

### 1. Authentication Security
- Firebase Authentication with email/password
- JWT token-based session management
- Secure password requirements
- Account verification via email

### 2. Data Security
- Firestore security rules
- Input validation and sanitization
- XSS and CSRF protection
- Encrypted data transmission (HTTPS)

### 3. Payment Security
- PCI DSS compliant payment processing
- Secure API key management
- Payment signature verification
- Fraud detection mechanisms

### 4. Firebase Security Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Quotations are accessible to authenticated users
    match /quotations/{quotationId} {
      allow read, write: if request.auth != null;
    }
    
    // Subscriptions are read-only for users
    match /subscriptions/{subscriptionId} {
      allow read: if request.auth != null;
      allow write: if false; // Only server can write
    }
  }
}
```

## Performance Architecture

### 1. Frontend Performance
- **Lazy Loading**: Components and images loaded on demand
- **Code Splitting**: Separate bundles for different routes
- **Caching**: Browser caching for static assets
- **Image Optimization**: WebP format and responsive images
- **Bundle Optimization**: Tree shaking and minification

### 2. Backend Performance
- **Database Indexing**: Optimized Firestore queries
- **CDN Integration**: Firebase Hosting with global CDN
- **Caching Strategy**: Browser and server-side caching
- **Connection Pooling**: Efficient database connections

### 3. Mobile Performance
- **Native Performance**: Flutter's compiled native code
- **Memory Management**: Efficient widget lifecycle
- **Battery Optimization**: Background task management
- **Network Optimization**: Request batching and compression

## Scalability Architecture

### 1. Horizontal Scaling
- **Microservices**: Modular service architecture
- **Load Balancing**: Firebase automatic scaling
- **Database Sharding**: Firestore automatic partitioning
- **CDN Distribution**: Global content delivery

### 2. Vertical Scaling
- **Resource Optimization**: Efficient memory usage
- **Query Optimization**: Indexed database queries
- **Caching Layers**: Multiple levels of caching
- **Connection Optimization**: Persistent connections

## Monitoring and Analytics

### 1. Application Monitoring
- **Firebase Analytics**: User behavior tracking
- **Performance Monitoring**: App performance metrics
- **Crash Reporting**: Error tracking and reporting
- **Custom Events**: Business-specific analytics

### 2. Infrastructure Monitoring
- **Firebase Console**: Service health monitoring
- **Usage Analytics**: Resource utilization tracking
- **Cost Monitoring**: Service usage and billing
- **Security Monitoring**: Authentication and access logs

## Deployment Architecture

### 1. Web Deployment
```
Source Code → GitHub → Firebase Hosting → Global CDN → Users
```

### 2. Mobile Deployment
```
Source Code → Build Pipeline → App Stores → User Devices
```

### 3. CI/CD Pipeline
```
Code Commit → Automated Tests → Build → Deploy → Monitor
```

## Technology Stack Summary

### Frontend Technologies
- **Flutter**: Cross-platform UI framework
- **Dart**: Programming language
- **Provider**: State management
- **Go Router**: Navigation management

### Backend Technologies
- **Firebase**: Backend-as-a-Service
- **Firestore**: NoSQL database
- **Firebase Auth**: Authentication service
- **Firebase Storage**: File storage
- **Firebase Hosting**: Web hosting

### Third-Party Integrations
- **Razorpay**: Payment gateway
- **Google Maps**: Location services
- **Email Services**: Quotation delivery
- **Analytics**: User behavior tracking

This technical architecture ensures scalability, security, and maintainability while providing a seamless user experience across all platforms.
